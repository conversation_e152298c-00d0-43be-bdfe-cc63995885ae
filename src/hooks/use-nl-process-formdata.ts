import useNLRequest from '@/hooks/use-nl-request';
import type { NLProcessInfo, ProcessTaskDraft } from '#/entity/process';
import { useMemo } from 'react';

export default function useNLProcessFormdata(channelUuid?: string) {
  const {data, isLoading, mutate} = useNLRequest<ProcessTaskDraft>(channelUuid ? ['/api/rest/processtask/draft/get', {channelUuid }] : null, {
    revalidateOnFocus: false,
  })
  const usefullInfo = useMemo(() => {
    const channelPath = data?.channelPath;
    const channelType = data?.channelType
    const channelVo = data?.channelVo;
    const processInfo: NLProcessInfo = {
      channelPath: channelPath || '',
      color: channelType?.color || '',
      icon: channelVo?.icon || '',
      description: channelType?.description || '',
    }
    return {
      processInfo,
      formConfig: data?.formConfig,
    }
  }, [data])
  
  return {
    data: usefullInfo,
    isLoading,
    refresh: mutate
  }
}