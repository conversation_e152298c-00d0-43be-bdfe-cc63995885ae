import type React from 'react';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import { ClassicEditor, Essentials, Paragraph, Bold, Italic, type EventInfo, Heading, FontSize, FontColor, FontBackgroundColor, Strikethrough, Code, Link, BlockQuote } from 'ckeditor5';
import 'ckeditor5/ckeditor5.css';
import './index.css'

const apiBaseUrl = import.meta.env.VITE_APP_API_BASE_URL;

// const formatList = [
//   'mp4',
//   'mkv',
//   'avi',
//   'mov',
//   'webm',
//   'flv',
//   'f4v',
//   'mpg',
//   'mpeg',
//   'ts',
//   'm2ts',
//   'mts',
//   'wmv',
//   'rm',
//   'rmvb',
//   '3pg',
//   '3g2'
// ]

const toolbar = [
  '|',
  'heading',
  '|',
  'fontSize',
  'fontColor',
  'fontBackgroundColor',
  '|',
  'bold',
  'italic',
  'strikethrough',
  'code',
  'link',
  'blockQuote',
  '|',
  // 'uploadImage',
  // 'uploadVideo',
  '|',
]

interface CKRichProps {
  value?: string;
  onChange?: (value: string) => void
}
const CKRich: React.FC<CKRichProps> = (props) => {
  const { value, onChange} = props;
  const triggerValue = (changedValue: string) => {
    onChange?.(changedValue)
  }
  const handleCKEditorChange = (_: EventInfo, editor: ClassicEditor) => {
    triggerValue(editor.getData())
  }
  return (
    <div className='max-w-[375px]'>
      <CKEditor
        editor={ClassicEditor}
        config={ {
          licenseKey: 'GPL', // Or 'GPL'.
          language: 'zh',
          ckfinder: {
            uploadUrl: `${apiBaseUrl}/v1/proxy/neatlogic/api/binary/image/upload`
          },
          // uploadVideoConfig: {
          //   url: `${apiBaseUrl}/v1/proxy/neatlogic/api/binary/file/upload`,
          //   formatList, // 支持上传的视频格式，例如mp4
          //   params: {
          //     type: 'framework', // 用于后台文档管理，标识模块或者功能等
          //     fileKey: 'file', // file对象的key，传递给后端参数的key
          //     uniqueKey: '', // 如果不为空，代表附件名唯一，相同名称的附件只会保留最新的一个
          //   }
          // },
          plugins: [ Essentials, Paragraph, Bold, Italic, Heading, FontSize, FontColor, FontBackgroundColor, Strikethrough, Code, Link, BlockQuote ],
          toolbar,
          initialData: value
        } }
        onChange={handleCKEditorChange}
      />
    </div>
  )
}

export default CKRich;
