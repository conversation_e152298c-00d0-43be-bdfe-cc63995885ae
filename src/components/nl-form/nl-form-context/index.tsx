import type { NLProcessInfo } from "#/entity/process";
import type { NLFormComponent } from "#/entity/process/nl-form-components";
import { createContext } from "react";
import type { FormProps } from 'antd-mobile'

interface NLFormContextState {
  /**
   * antd 的 form 实例
   */
  form: FormProps['form'] | null;
  /**
   * 表单组件列表
   */
  nlFormComponents?: NLFormComponent[]
  /**
   * 流程基本信息
   */
  processInfo: NLProcessInfo
}

export const NLFormContext = createContext<NLFormContextState>({
  form: null,
  nlFormComponents: [],
  processInfo: {
    channelPath: '',
    color: '',
    description: '',
    icon: ''
  }
})
