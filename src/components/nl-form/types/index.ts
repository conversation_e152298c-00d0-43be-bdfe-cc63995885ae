import type { NL<PERSON>ormComponent, NLForm<PERSON>omponent<PERSON><PERSON><PERSON>, NLFormHandler } from "#/entity/process/nl-form-components";
import type { FormItemProps } from "antd-mobile";

export interface NLFormItemProps extends FormItemProps {
  nlFormComponent: NLFormComponent
}

export interface NLFormItemValue {
  attributeUuid: string;
  key: string;
  handler: NLFormHandler;
  dataList: string | number | NLFormComponentMapping | Array<string | number> | Array<NLFormComponentMapping>
}

export interface NLFormElement {
  value?: NLFormItemValue;
  onChange?: (value: NLFormItemValue) => void
}