import type React from 'react';
import NLFormHeader from './nl-form-header';
import NLFormReminder from './nl-form-reminder';
import NLFormCore from './nl-form-core';
import NLFormFooter from './nl-form-footer';
import NLFormLayout from './nl-form-layout';
import NLFormBody from './nl-form-body';
import type { ProcessTaskFormConfig, NLProcessInfo } from '#/entity/process';

interface NLFormProps {
  formConfig?: ProcessTaskFormConfig;
  processInfo: NLProcessInfo
}

const NLForm: React.FC<NLFormProps> = (props) => {
  const { formConfig, processInfo } = props
  return (
    <NLFormLayout formConfig={formConfig} processInfo={processInfo}>
      <NLFormHeader />
      <NLFormBody>
        <NLFormReminder />
        <NLFormCore /> 
        <NLFormFooter />
      </NLFormBody>
    </NLFormLayout>
  );
};

export default NLForm;
