import { Button } from 'antd-mobile';
import type React from 'react';
import useNLForm from './hooks/use-nl-form';

const NLFormFooter: React.FC = () => {

  const { form } = useNLForm()
  return (
    <div className="mt-6 px-4 pt-4 border-t border-gray-200">
      <div className="flex space-x-3">
        <Button block color='primary' size="large" onClick={form?.submit}>
          提交申请
        </Button>
      </div>
    </div>
  )
}

export default NLFormFooter;
