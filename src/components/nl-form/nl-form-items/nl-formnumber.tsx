import type { NLFormComponent } from '#/entity/process/nl-form-components';
import { Form, Input, type FormItemProps } from 'antd-mobile';
import type React from 'react';
import classNames from 'classnames';
import { useMemo } from 'react';

interface NLFormNumberProps extends FormItemProps {
  nlFormComponent: NLFormComponent
}
const NLFormNumber: React.FC<NLFormNumberProps> = (props) => {
  const { nlFormComponent, ...rest} = props
  const config = nlFormComponent.config;
  const rules = useMemo(() => {
    const tempRules: FormItemProps['rules'] = [];
    if (config.isRequired) {
      tempRules.push({
        required: true,
      })
    }
    if (config.decimalNumber) {
      tempRules.push({
        pattern: new RegExp(`^-?\\d+(\\.\\d{0,${config.decimalNumber}})?$`),
        message: `小数位${config.decimalNumber}`
      })
    }
    return tempRules
  }, [config])
  return (
    <Form.Item
      key={nlFormComponent.key}
      name={nlFormComponent.key}
      label={nlFormComponent.label}
      help={config.description}
      required={config.isRequired}
      disabled={config.isDisabled}
      hidden={config.isHide}
      initialValue={config.defaultValue}
      rules={rules}
      {...rest}
      className={classNames({ 'blur-sm': config.isMask})}
    >
      <Input type="number" max={config.maxNumber} min={config.minNumber} readOnly={config.isReadOnly} placeholder={config.placeholder} maxLength={config.maxLength} />
    </Form.Item>
  )
}

export default NLFormNumber;
