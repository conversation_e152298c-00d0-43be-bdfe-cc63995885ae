import type { NLFormHandler } from "#/entity/process/nl-form-components";
import { Form, Input } from "antd-mobile";
import NLFormText from "./nl-formtext";
import NLFormTextArea from "./nl-formtextarea";
import type { NLFormItemProps } from "../types";
import React from "react";
import NLFormCKEditor from "./nl-formckeditor";
import NLFormNumber from "./nl-formnumber";
import NLFormPassword from "./nl-formpassword";
import NLFormSelect from "./nl-formselect";
import NLFormCascader from "./nl-formcascader";
import NLFormRadio from "./nl-formradio";
import NLFormCheckbox from "./nl-formcheckbox";
import NLFormDate from "./nl-formdate";
import NLFormTime from "./nl-formtime";
import NLFormLink from "./nl-formlink";
import NLFormRate from "./nl-formrate";

// 只有 nlFormComponent 有变化时，才重新渲染
const getMemoizedNLFormItem = (Component: React.FC<NLFormItemProps>) => React.memo(Component, (prevProps, nextProps) => {
  const prevNLFormComponentJson =  JSON.stringify(prevProps.nlFormComponent)
  const nextNLFormComponentJson =  JSON.stringify(nextProps.nlFormComponent)
  return prevNLFormComponentJson === nextNLFormComponentJson
})

export default {
  formtext: getMemoizedNLFormItem(NLFormText),
  formtextarea: getMemoizedNLFormItem(NLFormTextArea),
  formckeditor: getMemoizedNLFormItem(NLFormCKEditor),
  formnumber: getMemoizedNLFormItem(NLFormNumber),
  formpassword: getMemoizedNLFormItem(NLFormPassword),
  formselect: getMemoizedNLFormItem(NLFormSelect),
  formcascader: getMemoizedNLFormItem(NLFormCascader),
  formradio: getMemoizedNLFormItem(NLFormRadio),
  formcheckbox: getMemoizedNLFormItem(NLFormCheckbox),
  formdate: getMemoizedNLFormItem(NLFormDate),
  formtime: getMemoizedNLFormItem(NLFormTime),
  formlink: getMemoizedNLFormItem(NLFormLink),
  formrate: getMemoizedNLFormItem(NLFormRate),
  formdivider: () => <div>formdivider</div>,
  formcollapse: () => <div>formcollapse</div>,
  formtab: () => <div>formtab</div>
} as Partial<Record<NLFormHandler, React.FC<NLFormItemProps>>>


/**
 * 兜底组件
 */
export const NLSafetyFormItem: React.FC<NLFormItemProps> = ({nlFormComponent, ...rest}) => {
  return (
    <Form.Item
      key={nlFormComponent.key}
      name={nlFormComponent.key}
      label={nlFormComponent.label}
      help={nlFormComponent.config.description}
      {...rest}
    >
      <Input placeholder="这个是兜底组件" />
    </Form.Item>
  )
}
