import type { NLFormComponent, NLFormComponentMapping } from '#/entity/process/nl-form-components';
import { Checkbox, Form, Space, type FormItemProps } from 'antd-mobile';
import type React from 'react';
import classNames from 'classnames';
import { useMemo } from 'react';
import useNLMatrix from '../hooks/use-nl-matrix';

interface NLFormCheckboxProps extends FormItemProps {
  nlFormComponent: NLFormComponent
}
const NLFormCheckbox: React.FC<NLFormCheckboxProps> = (props) => {
  const { nlFormComponent, ...rest} = props
  const config = nlFormComponent.config;
  const dataSourceType = config.dataSource
  const staticDataList = config.dataList || []
  const matrixData = useNLMatrix(config)
  const rules = useMemo(() => {
    const tempRules: FormItemProps['rules'] = [];
    if (config.isRequired) {
      tempRules.push({
        required: true,
      })
    }
    return tempRules
  }, [config])

  const options = useMemo(() => {
    let sourceData: NLFormComponentMapping[] = [];
    
    if (dataSourceType === 'static' && staticDataList) {
      sourceData = staticDataList
    } else if (dataSourceType === 'matrix' && matrixData) {
      sourceData = matrixData;
    }
    return sourceData
  }, [staticDataList, dataSourceType, matrixData])
  return (
    <Form.Item
      key={nlFormComponent.key}
      name={nlFormComponent.key}
      label={nlFormComponent.label}
      help={config.description}
      required={config.isRequired}
      disabled={config.isDisabled}
      hidden={config.isHide}
      initialValue={config.defaultValue}
      rules={rules}
      {...rest}
      className={classNames({ 'blur-sm': config.isMask})}
    >
      <Checkbox.Group>
        <Space direction='vertical'>
          {options.map((item) => {
            return <Checkbox key={item.value} value={item.value}>{item.text}</Checkbox>
          })}
        </Space>
      </Checkbox.Group>
    </Form.Item>
  )
}

export default NLFormCheckbox;
