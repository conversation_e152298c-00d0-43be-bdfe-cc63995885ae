import type { NLFormComponent } from '#/entity/process/nl-form-components';
import classNames from 'classnames';
import type React from 'react';

interface NLFormLinkProps {
  nlFormComponent: NLFormComponent
}
const NLFormLink: React.FC<NLFormLinkProps> = (props) => {
  const { nlFormComponent } = props
  const config = nlFormComponent.config;
  
  return (
    <div className={classNames({ 'blur-sm': config.isMask}, 'px-4 pb-4 text-brand')}>
      <a href={config.value} target={config.target} >{config.text}</a>
    </div>
  )
}

export default NLFormLink;
