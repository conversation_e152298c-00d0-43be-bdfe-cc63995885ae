import type React from 'react';
import type { NLFormElement } from '../../types';
import { ImageUploader, type ImageUploaderProps } from 'antd-mobile';

const Upload: React.FC<NLFormElement> = (props) => {
  const {value, onChange} = props
  const handleUpload: ImageUploaderProps['upload'] = async (file) => {
    console.log(file);
    return {
      url: '',
    }
  }
  return (
    <ImageUploader upload={handleUpload} />
  )
}

export default Upload;
