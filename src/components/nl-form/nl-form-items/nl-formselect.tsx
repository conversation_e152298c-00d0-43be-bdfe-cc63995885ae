import type { NLFormComponent, NLFormComponentMapping } from '#/entity/process/nl-form-components';
import { Form, type FormItemProps } from 'antd-mobile';
import type React from 'react';
import classNames from 'classnames';
import { useMemo } from 'react';
import PopupCheckList from '@/components/popup-check-list';
import useNLMatrix from '../hooks/use-nl-matrix';

interface NLFormSelectProps extends FormItemProps {
  nlFormComponent: NLFormComponent
}

const NLFormSelect: React.FC<NLFormSelectProps> = (props) => {
  const { nlFormComponent, ...rest} = props
  const config = nlFormComponent.config;
  const dataSourceType = config.dataSource
  const staticDataList = config.dataList || []
  const isMultiple = config.isMultiple; // 是否多选
  const matrixData = useNLMatrix(config)
  
  const rules = useMemo(() => {
    const tempRules: FormItemProps['rules'] = [];
    if (config.isRequired) {
      tempRules.push({
        required: true,
        message: `请选择${nlFormComponent.label}`
      })
    }
    return tempRules
  }, [config.isRequired, nlFormComponent.label])
  
  // 处理默认值
  const defaultValue = useMemo(() => {
    const d = config.defaultValue
    if (!d) return []
    
    if (typeof d === 'string') {
      return [d]
    } 
    
    if (d?.value) {
      return [d.value]
    }
    
    return []
  }, [config.defaultValue])

  // 构建选项数据
  const options = useMemo(() => {
    let sourceData: NLFormComponentMapping[] = [];
    
    if (dataSourceType === 'static' && staticDataList) {
      sourceData = staticDataList
    } else if (dataSourceType === 'matrix' && matrixData) {
      sourceData = matrixData;
    }
    return sourceData
  }, [staticDataList, dataSourceType, matrixData])

  return (
    <Form.Item
      key={nlFormComponent.key}
      name={nlFormComponent.key}
      label={nlFormComponent.label}
      help={config.description}
      required={config.isRequired}
      disabled={config.isDisabled}
      hidden={config.isHide}
      initialValue={defaultValue}
      rules={rules}
      onClick={(_, ref) => {
        ref.current?.open()
      }}
      {...rest}
      className={classNames({ 'blur-sm': config.isMask})}
    >
      <PopupCheckList options={options} multiple={isMultiple} />
    </Form.Item>
  )
}

export default NLFormSelect;