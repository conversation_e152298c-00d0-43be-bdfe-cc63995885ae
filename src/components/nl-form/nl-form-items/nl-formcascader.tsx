import type { NLFormComponent } from '#/entity/process/nl-form-components';
import { Cascader, Form, type CascadePickerOption, type CascadePickerRef, type FormItemProps } from 'antd-mobile';
import type React from 'react';
import classNames from 'classnames';
import { useMemo, type RefObject } from 'react';

interface NLFormCascaderProps extends FormItemProps {
  nlFormComponent: NLFormComponent
}
const NLFormCascader: React.FC<NLFormCascaderProps> = (props) => {
  const { nlFormComponent, ...rest} = props
  const config = nlFormComponent.config;
  const rules = useMemo(() => {
    const tempRules: FormItemProps['rules'] = [];
    if (config.isRequired) {
      tempRules.push({
        required: true,
      })
    }
    return tempRules
  }, [config.isRequired])
  const dataList = config.dataList
  const antdCascaderData = useMemo(() => {
    if (!dataList) {
      return []
    }
    const res: CascadePickerOption[] = dataList.map((item) => {
      return {
        label: item.text,
        value: item.value,
        children: item.children?.map((child) => {
          return {
            label: child.text,
            value: child.value,
          }
        }) || []
      }
    })
    return res
  }, [dataList])

  return (
    <Form.Item
      key={nlFormComponent.key}
      name={nlFormComponent.key}
      label={nlFormComponent.label}
      help={config.description}
      required={config.isRequired}
      disabled={config.isDisabled}
      hidden={config.isHide}
      initialValue={config.defaultValue}
      rules={rules}
      {...rest}
      className={classNames({ 'blur-sm': config.isMask})}
      onClick={(_, pickerRef: RefObject<CascadePickerRef>) => {
        pickerRef?.current?.open()
      }}
    >
      <Cascader
        className='bg-white'
        options={antdCascaderData}
      >
        {items => {
          if (items.length === 0) {
            return <span className='text-t-description'>{config.placeholder || '未选择'}</span>
          }
          return items.map(item => item?.label ?? 'config.placeholder').join('-')
        }}
      </Cascader>
    </Form.Item>
  )
}

export default NLFormCascader;
