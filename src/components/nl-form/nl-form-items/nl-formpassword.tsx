import type { NLFormComponent } from '#/entity/process/nl-form-components';
import { Form, Input, type FormItemProps } from 'antd-mobile';
import type React from 'react';
import classNames from 'classnames';
import { useMemo, useState } from 'react';
import { AiFillEye, AiFillEyeInvisible } from 'react-icons/ai';

interface NLFormPasswordProps extends FormItemProps {
  nlFormComponent: NLFormComponent
}
const NLFormPassword: React.FC<NLFormPasswordProps> = (props) => {
  const { nlFormComponent, ...rest} = props
  const [visible,setVisible] = useState(false)
  const config = nlFormComponent.config;
  const rules = useMemo(() => {
    const tempRules: FormItemProps['rules'] = [];
    if (config.isRequired) {
      tempRules.push({
        required: true,
      })
    }
    return tempRules
  }, [config])
  return (
    <Form.Item
      key={nlFormComponent.key}
      name={nlFormComponent.key}
      label={nlFormComponent.label}
      help={config.description}
      required={config.isRequired}
      disabled={config.isDisabled}
      hidden={config.isHide}
      initialValue={config.defaultValue}
      rules={rules}
      {...rest}
      className={classNames({ 'blur-sm': config.isMask})}
      extra={
        <div className='p-1 cursor-pointer text-xl'>
          {!visible ? (
            <AiFillEyeInvisible onClick={() => setVisible(true)} />
          ) : (
            <AiFillEye onClick={() => setVisible(false)} />
          )}
        </div>
      }
    >
      <Input type={visible ? 'text' : 'password'} readOnly={config.isReadOnly} placeholder={config.placeholder} maxLength={config.maxLength} />
    </Form.Item>
  )
}

export default NLFormPassword;
