import type { NLFormComponent } from '#/entity/process/nl-form-components';
import { Form, type FormItemProps } from 'antd-mobile';
import type React from 'react';
import classNames from 'classnames';
import { useMemo } from 'react';
import CKRich from '@/components/ck-rich';

interface NLFormCKEditorProps extends FormItemProps {
  nlFormComponent: NLFormComponent
}
const NLFormCKEditor: React.FC<NLFormCKEditorProps> = (props) => {
  const { nlFormComponent, ...rest} = props
  const config = nlFormComponent.config;
  const rules = useMemo(() => {
    const tempRules: FormItemProps['rules'] = [];
    if (config.isRequired) {
      tempRules.push({
        required: true,
      })
    }
    return tempRules
  }, [config])
  return (
    <Form.Item
      key={nlFormComponent.key}
      name={nlFormComponent.key}
      label={nlFormComponent.label}
      help={config.description}
      required={config.isRequired}
      disabled={config.isDisabled}
      hidden={config.isHide}
      initialValue={config.defaultValue}
      rules={rules}
      {...rest}
      className={classNames({ 'blur-sm': config.isMask})}
    >
      <CKRich />
    </Form.Item>
  )
}

export default NLFormCKEditor;
