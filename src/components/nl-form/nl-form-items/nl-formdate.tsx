import type { NLFormComponent } from '#/entity/process/nl-form-components';
import { DatePicker, Form,  Space,  type DatePickerProps,  type DatePickerRef, type FormItemProps } from 'antd-mobile';
import type React from 'react';
import classNames from 'classnames';
import { useCallback, useMemo, type RefObject } from 'react';
import dayjs from 'dayjs';

type NLFormDatePrecision = 'year' | 'month' | 'day' | 'hour' | 'minute' | 'second';

// 日期校验规则类型定义
interface DateValidRule {
  filter: 'custom' | 'nowDay' | string; // 'custom'=自定义日期, 'nowDay'=当前时间, 其他=表单组件UUID
  text: 'later' | 'earlier' | 'laterAndEqual' | 'earlierAndEqual'; // 比较类型
  value: string | number; // 比较值或偏移量
  unit?: 'minute' | 'hour' | 'day' | 'month' | 'year'; // 时间单位
}

const renderLabel: DatePickerProps['renderLabel'] = (type, data) => {
  const affixMap:  Partial<Record<NLFormDatePrecision | 'now', string>> = {
    year: '年',
    month: '月',
    day: '日',
    hour: '时',
    minute: '分',
    second: '秒',
  }
  return `${data}${affixMap[type as NLFormDatePrecision] || ''}`
}

interface NLFormDateProps extends FormItemProps {
  nlFormComponent: NLFormComponent
}
const NLFormDate: React.FC<NLFormDateProps> = (props) => {
  const { nlFormComponent, ...rest } = props
  const config = nlFormComponent.config;

  const correctFormat = config.format?.replace('dd', 'DD').replace('yyyy', 'YYYY') || 'YYYY-MM-DD';

  // 计算日期偏移
  const calculateDate = useCallback((unit: string, value: number, baseDate?: string | Date) => {
    const base = baseDate ? dayjs(baseDate) : dayjs();

    switch (unit) {
      case 'minute':
        return base.add(value, 'minute');
      case 'hour':
        return base.add(value, 'hour');
      case 'day':
        return base.add(value, 'day');
      case 'month':
        return base.add(value, 'month');
      case 'year':
        return base.add(value, 'year');
      default:
        return base;
    }
  }, []);

  // 工作日校验函数
  const isWorkday = useCallback((date: Date | string) => {
    const dayOfWeek = dayjs(date).day();
    // 0 = 周日, 6 = 周六，工作日为 1-5
    return dayOfWeek >= 1 && dayOfWeek <= 5;
  }, []);

  // 生成日期校验规则
  const dateValidationRules = useMemo(() => {
    const tempRules: FormItemProps['rules'] = [];

    // 必填校验
    if (config.isRequired) {
      tempRules.push({
        required: true,
        message: `请选择${nlFormComponent.label}`
      });
    }

    // 自定义日期校验
    if (config.validValueList && Array.isArray(config.validValueList)) {
      for (const rule of config.validValueList as DateValidRule[]) {
        if (!rule.filter || !rule.text) continue;

        const dateText = {
          later: '晚于',
          earlier: '早于',
          laterAndEqual: '晚于等于',
          earlierAndEqual: '早于等于'
        };

        const unitText = {
          minute: '分钟',
          hour: '小时',
          day: '天',
          month: '月',
          year: '年'
        };

        let message = '';
        let validatorFn: ((rule: any, value: any) => Promise<void>) | null = null;

        if (rule.filter === 'custom' && rule.value) {
          // 自定义日期比较
          message = `日期需要${dateText[rule.text]}${rule.value}`;

          validatorFn = (_, value) => {
            if (!value) return Promise.resolve();

            const currentValue = dayjs(value);
            const compareValue = dayjs(rule.value as string);

            let isValid = false;
            switch (rule.text) {
              case 'later':
                isValid = currentValue.isAfter(compareValue);
                break;
              case 'earlier':
                isValid = currentValue.isBefore(compareValue);
                break;
              case 'laterAndEqual':
                isValid = currentValue.isAfter(compareValue) || currentValue.isSame(compareValue);
                break;
              case 'earlierAndEqual':
                isValid = currentValue.isBefore(compareValue) || currentValue.isSame(compareValue);
                break;
              default:
                isValid = true;
            }

            return isValid ? Promise.resolve() : Promise.reject(new Error(message));
          };
        } else if (rule.filter === 'nowDay') {
          // 相对当前时间比较
          message = `日期需要${dateText[rule.text]}当前时间${rule.value}${unitText[rule.unit || 'day']}`;

          validatorFn = (_, value) => {
            if (!value) return Promise.resolve();

            const currentValue = dayjs(value);
            const compareValue = calculateDate(rule.unit || 'day', Number(rule.value) || 0);

            let isValid = false;
            switch (rule.text) {
              case 'later':
                isValid = currentValue.isAfter(compareValue);
                break;
              case 'earlier':
                isValid = currentValue.isBefore(compareValue);
                break;
              case 'laterAndEqual':
                isValid = currentValue.isAfter(compareValue) || currentValue.isSame(compareValue);
                break;
              case 'earlierAndEqual':
                isValid = currentValue.isBefore(compareValue) || currentValue.isSame(compareValue);
                break;
              default:
                isValid = true;
            }

            return isValid ? Promise.resolve() : Promise.reject(new Error(message));
          };
        }
        // 注意：表单组件间的比较需要访问其他表单字段的值，这里暂时不实现
        // 因为需要通过 Form 实例获取其他字段的值，比较复杂

        if (validatorFn) {
          tempRules.push({
            validator: validatorFn,
            message
          });
        }
      }
    }

    // 工作日校验
    if (config.validType && Array.isArray(config.validType) && config.validType.includes('workdate')) {
      tempRules.push({
        validator: (_, value) => {
          if (!value) return Promise.resolve();

          if (!isWorkday(value)) {
            return Promise.reject(new Error('请选择工作日（周一至周五）'));
          }

          return Promise.resolve();
        },
        message: '请选择工作日（周一至周五）'
      });
    }

    return tempRules;
  }, [config.isRequired, config.validValueList, config.validType, nlFormComponent.label, calculateDate, isWorkday]);

  const precision: NLFormDatePrecision = useMemo(() => {
    if (!correctFormat) {
      return 'day'
    }
    if (correctFormat.includes('mm')) {
      return 'minute'
    }
    if (correctFormat.includes('HH')) {
      return 'hour'
    }
    if (correctFormat.includes('DD')) {
      return 'day'
    }
    if (correctFormat.includes('MM')) {
      return 'month'
    }
    if (correctFormat.includes('YYYY')) {
      return 'year'
    }
    return 'day'
  }, [correctFormat])

  const formatDate = useCallback((date: Date) => {
    return dayjs(date).format(correctFormat)
  }, [correctFormat])

  // 处理默认值
  const defaultValue = useMemo(() => {
    if (!config.defaultValue) return undefined;

    if (typeof config.defaultValue === 'string') {
      const parts = config.defaultValue.split('_');
      const type = parts[0]; // equal, less-than, greater-than, others

      switch (type) {
        case 'equal': {
          // equal_2025-08-20 00:00 - 填写当天（指定日期）
          if (parts[1]) {
            return dayjs(parts[1]).toDate();
          }
          return new Date(); // 如果没有指定日期，使用当前时间
        }

        case 'less-than': {
          // less-than_2025-08-20 00:00_3_year - 填写当天前（指定日期前N个时间单位）
          if (parts[1] && parts[2] && parts[3]) {
            const baseDate = dayjs(parts[1]);
            const offset = -Number(parts[2]); // 负数表示往前
            const unit = parts[3] as any; // year, month, day, hour, minute
            return calculateDate(unit, offset, baseDate.toDate()).toDate();
          }
          break;
        }

        case 'greater-than': {
          // greater-than_2025-08-20 00:00_4_month - 填写当天后（指定日期后N个时间单位）
          if (parts[1] && parts[2] && parts[3]) {
            const baseDate = dayjs(parts[1]);
            const offset = Number(parts[2]); // 正数表示往后
            const unit = parts[3] as any; // year, month, day, hour, minute
            return calculateDate(unit, offset, baseDate.toDate()).toDate();
          }
          break;
        }

        case 'others': {
          // others_2025-08-20 00:00 - 指定日期
          if (parts[1]) {
            return dayjs(parts[1]).toDate();
          }
          break;
        }

        default: {
          // 尝试直接解析日期字符串
          const parsed = dayjs(config.defaultValue);
          return parsed.isValid() ? parsed.toDate() : undefined;
        }
      }
    }

    return undefined;
  }, [config.defaultValue, calculateDate]);

  // 调试日志

  return (
    <Form.Item
      key={nlFormComponent.key}
      name={nlFormComponent.key}
      label={nlFormComponent.label}
      help={config.description}
      required={config.isRequired}
      disabled={config.isDisabled}
      hidden={config.isHide}
      initialValue={defaultValue}
      rules={dateValidationRules}
      trigger='onConfirm'
      {...rest}
      className={classNames({ 'blur-sm': config.isMask})}
      onClick={(_, pickerRef: RefObject<DatePickerRef>) => {
        pickerRef.current?.open();
      }}
    >
      <DatePicker
        className='bg-white'
        precision={precision}
        renderLabel={renderLabel}
      >
        {(items) => {
          return (
            <Space align='center'>
              {!items
                ? <span className='text-t-description'>{config.placeholder}</span>
                : <span>{formatDate(items)}</span>}
            </Space>
          )
        }}
      </DatePicker>
    </Form.Item>
  )
}

export default NLFormDate;
