import type { NLFormComponent } from '#/entity/process/nl-form-components';
import { Picker, Form,  type DatePickerRef, type FormItemProps, type PickerProps } from 'antd-mobile';
import type React from 'react';
import classNames from 'classnames';
import { useMemo, useCallback, type RefObject } from 'react';
import dayjs from 'dayjs';

// 时间校验规则类型定义
interface TimeValidRule {
  filter: 'custom' | string; // 'custom'=自定义时间, 其他=表单组件UUID
  text: 'later' | 'earlier' | 'belong' | 'notbelong'; // 比较类型
  value: string | string[]; // 比较值，可能是单个时间或时间范围
}

interface NLFormTimeProps extends FormItemProps {
  nlFormComponent: NLFormComponent
}


const fullTimePickerColumn: PickerProps['columns'] = [
  Array.from({ length: 24 }, (_, i) => ({label: `${i.toString().padStart(2, '0')}时`, value: i.toString().padStart(2, '0')})),
  Array.from({ length: 60 }, (_, i) => ({label: `${i.toString().padStart(2, '0')}分`, value: i.toString().padStart(2, '0')})),
  Array.from({ length: 60 }, (_, i) => ({label: `${i.toString().padStart(2, '0')}秒`, value: i.toString().padStart(2, '0')})),
]

const NLFormTime: React.FC<NLFormTimeProps> = (props) => {
  const { nlFormComponent, ...rest } = props
  const config = nlFormComponent.config;

  // 判断时间是否在指定范围内
  const isTimeInRange = useCallback((time: string, range: string[]) => {
    const [startTimeStr, endTimeStr] = range;
    const today = dayjs().format('YYYY-MM-DD');

    const startTime = dayjs(`${today} ${startTimeStr}`);
    const endTime = dayjs(`${today} ${endTimeStr}`);
    const timeToCheck = dayjs(`${today} ${time}`);

    return (timeToCheck.isAfter(startTime) || timeToCheck.isSame(startTime)) &&
           (timeToCheck.isBefore(endTime) || timeToCheck.isSame(endTime));
  }, []);

  // 时间校验规则
  const timeValidationRules = useMemo(() => {
    const tempRules: FormItemProps['rules'] = [];

    // 必填校验
    if (config.isRequired) {
      tempRules.push({
        required: true,
        message: `请选择${nlFormComponent.label}`
      });
    }

    // 自定义时间校验
    if (config.validType && Array.isArray(config.validType) && config.validType.includes('custom') &&
        config.validValueList && Array.isArray(config.validValueList)) {

      for (const rule of config.validValueList as TimeValidRule[]) {
        if (!rule.filter || !rule.text) continue;

        const timeText = {
          later: '晚于',
          earlier: '早于',
          belong: '属于',
          notbelong: '不属于'
        };

        let message = '';
        let validatorFn: ((rule: any, value: any) => Promise<void>) | null = null;

        if (rule.filter === 'custom' && rule.value) {
          // 自定义时间比较
          if (rule.text === 'belong' || rule.text === 'notbelong') {
            // 时间范围校验
            if (Array.isArray(rule.value) && rule.value.length === 2) {
              message = `时间需要${timeText[rule.text]}${rule.value[0]}~${rule.value[1]}`;

              validatorFn = (_, value) => {
                if (!value || !Array.isArray(value)) return Promise.resolve();

                // 将选择器的值转换为时间字符串 ['08', '30'] -> '08:30'
                const timeStr = value.join(':');
                const isInRange = isTimeInRange(timeStr, rule.value as string[]);
                const isValid = rule.text === 'belong' ? isInRange : !isInRange;

                return isValid ? Promise.resolve() : Promise.reject(new Error(message));
              };
            }
          } else {
            // 单个时间比较
            const compareTime = Array.isArray(rule.value) ? rule.value[0] : rule.value;
            message = `时间需要${timeText[rule.text]}${compareTime}`;

            validatorFn = (_, value) => {
              if (!value || !Array.isArray(value)) return Promise.resolve();

              const timeStr = value.join(':');
              const currentTime = dayjs(`2000-01-01 ${timeStr}`);
              const compareTimeObj = dayjs(`2000-01-01 ${compareTime}`);

              let isValid = false;
              switch (rule.text) {
                case 'later':
                  isValid = currentTime.isAfter(compareTimeObj);
                  break;
                case 'earlier':
                  isValid = currentTime.isBefore(compareTimeObj);
                  break;
                default:
                  isValid = true;
              }

              return isValid ? Promise.resolve() : Promise.reject(new Error(message));
            };
          }
        }
        // 注意：表单组件间的比较需要访问其他表单字段的值，这里暂时不实现
        // 因为需要通过 Form 实例获取其他字段的值，比较复杂

        if (validatorFn) {
          tempRules.push({
            validator: validatorFn,
            message
          });
        }
      }
    }

    return tempRules;
  }, [config.isRequired, config.validType, config.validValueList, nlFormComponent.label, isTimeInRange]);

  const timePickerColumn = useMemo(() => {
    const format = config.format
    if (format?.includes('ss')) {
      return fullTimePickerColumn
    }
    return [fullTimePickerColumn[0], fullTimePickerColumn[1]]
  }, [config.format])

  const defaultValue = (config.defaultValue as string)?.split(':') || []

  return (
    <Form.Item
      key={nlFormComponent.key}
      name={nlFormComponent.key}
      label={nlFormComponent.label}
      help={config.description}
      required={config.isRequired}
      disabled={config.isDisabled}
      hidden={config.isHide}
      initialValue={defaultValue}
      rules={timeValidationRules}
      trigger='onConfirm'
      {...rest}
      className={classNames({ 'blur-sm': config.isMask})}
      onClick={(_, pickerRef: RefObject<DatePickerRef>) => {
        pickerRef.current?.open();
      }}
    >
      <Picker
        className='bg-white'
        columns={timePickerColumn}
      >
        {(items) => {
          if (!items?.length) {
            return <span className='text-t-description'>{config.placeholder}</span>
          }
          const compactLabels = items.map((item) => item?.label).join('')
          return <span>{compactLabels}</span>
        }}
      </Picker>
    </Form.Item>
  )
}

export default NLFormTime;
