import { useMemo, type PropsWithChildren } from 'react';
import type React from 'react';
import { NLFormContext } from './nl-form-context'
import type { NLFormComponent } from '#/entity/process/nl-form-components';
import { Form } from 'antd-mobile';
import type { NLProcessInfo, ProcessTaskFormConfig } from '#/entity/process';

interface NLFormLayoutProps extends PropsWithChildren {
  formConfig?: ProcessTaskFormConfig
  processInfo: NLProcessInfo
}
const NLFormLayout: React.FC<NLFormLayoutProps> = (props) => {
  const { children, formConfig, processInfo } = props;
  const [form] = Form.useForm()
  const nlFormComponents = useMemo(() => {
    const nlFormComponentsForMobile: NLFormComponent[] = []
    // 移动端不渲染标签组件，还可以忽略管理端定义的表格结构
    formConfig?.tableList.map((item) => {
      if (item.component && item.component.handler !== 'formlabel') {
        nlFormComponentsForMobile.push(item.component)
      }
    })
    return nlFormComponentsForMobile
  }, [formConfig])

  return (
    <NLFormContext.Provider value={{ form, nlFormComponents, processInfo}}>
      <div className='min-h-screen bg-gray-50'>
        {children}
      </div>
    </NLFormContext.Provider>
  )
}

export default NLFormLayout;

