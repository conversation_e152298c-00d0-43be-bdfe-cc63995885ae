import { Form, type FormProps } from 'antd-mobile';
import type React from 'react';
import nlFormItemsMap, { NLSafetyFormItem } from './nl-form-items';
import useNLForm from './hooks/use-nl-form';
import { useCallback } from 'react';

interface NLFormCoreProps extends FormProps {
}


const NLFormCore: React.FC<NLFormCoreProps> = (props) => {
  const { nlFormComponents, form} = useNLForm()
  const handleFinish = useCallback((value: any) => {
    console.log(value);
  }, [])
  if (!form) {
    return null
  }
  return (
    <Form
      layout='vertical'
      form={form}
      onFinish={handleFinish}
      {...props}
    >
      {
        nlFormComponents?.map((item) => {
          const Component = nlFormItemsMap[item.handler] || NLSafetyFormItem
          return <Component key={item.uuid} nlFormComponent={item} />
        })
      }
    </Form>
  )
}

export default NLFormCore;
