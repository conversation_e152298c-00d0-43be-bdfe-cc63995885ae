import type { NLFormComponentConfig, NLFormComponentMapping } from '#/entity/process/nl-form-components';
import { useNLRequest } from '@/hooks/use-nl-request';


function useNLMatrix(config: NLFormComponentConfig) {
  const dataSourceType = config.dataSource
  const matrixUuid = config.matrixUuid;
  const mapping = config.mapping
  const textField = mapping?.text;
  const valueField = mapping?.value;
  const { data } = useNLRequest<{ dataList?: NLFormComponentMapping[]}>(
    dataSourceType === 'matrix' ? ['/api/rest/matrix/column/data/search/forselect', {
      currentPage: 1, 
      pageSize: 1000, 
      matrixUuid,
      textField,
      valueField
    }] : null,
    {
      revalidateOnFocus: false
    }
  )

  return data?.dataList || []
}

export default useNLMatrix;