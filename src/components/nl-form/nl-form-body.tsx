import type { PropsWithChildren } from 'react';
import type React from 'react';
import useNLForm from './hooks/use-nl-form';
import NLFormEmptyContent from './nl-form-empty-content';

interface NLFormBodyProps extends PropsWithChildren {

}
const NLFormBody: React.FC<NLFormBodyProps> = (props) => {
  const { nlFormComponents} = useNLForm()
  if (!nlFormComponents?.length) {
    return <NLFormEmptyContent />
  }
  return (
    <div className="py-4">
      {props.children}
    </div>
  )
}

export default NLFormBody;
