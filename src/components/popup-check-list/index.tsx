import { <PERSON><PERSON>, <PERSON><PERSON>ist, <PERSON>up, SearchBar, Space, type CheckListProps } from 'antd-mobile';
import type React from 'react';
import { forwardRef, useImperativeHandle, useMemo, useState } from 'react';

interface CheckListOption {
  text: string;
  value:CheckListValue
}

type CheckListValue = string | number

interface PopupCheckListProps {
  options?: CheckListOption[];
  value?: CheckListValue[];
  onChange?: (value: CheckListValue[]) => void
  multiple?: boolean
}
const PopupCheckList: React.FC<PopupCheckListProps> = forwardRef<any, PopupCheckListProps>((props, ref) => {
  const { value, multiple, options = [], onChange } = props
  const [visible, setVisible] = useState(false)
  const [selected, setSelected] = useState(value)
  const [searchText, setSearchText] = useState('')

  const triggerChange = (v: CheckListValue[]) => {
    onChange?.(v)
  }
  const open = () => {
    setVisible(true)
  }

  const close = () => {
    setVisible(false)
  }

  useImperativeHandle(ref, () => ({
    open
  }))

  const handleChange: CheckListProps['onChange'] = (v: CheckListValue[]) => {
    setSelected(v)
    triggerChange(v)
  }

  const filteredItems = useMemo(() => {
    if (searchText) {
      return options.filter(item => item.text.includes(searchText))
    }
    return options
  }, [options, searchText])

  const selectedTexts = useMemo(() => {
    return options.filter(item => selected?.includes(item.value)).map(item => item.text).join(',')
  }, [selected, options])
  return (
    <div className=''>
      <Space align='center'>
        <div className='w-[60vw] text-ellipsis overflow-hidden text-nowrap'>{selectedTexts}</div>
      </Space>
      <Popup
        visible={visible}
        onMaskClick={() => {
          setVisible(false)
        }}
        position='bottom'
        destroyOnClose
      >
        <div className='bg-white'>
          <div className='flex justify-between pt-2'>
            <Button type='button' fill='none' onClick={close}>关闭</Button>
            <Button disabled={selected?.length === 0} type='button' fill='none' color='primary' onClick={close}>选好了</Button>
          </div>
          <div className="p-3 border-b">
            <SearchBar
              placeholder='输入文字过滤选项'
              value={searchText}
              onChange={v => {
                setSearchText(v)
              }}
            />
          </div>
          <div className="h-80 overflow-y-auto">
            <CheckList
              defaultValue={selected ? selected : []}
              multiple={multiple}
              onChange={handleChange}
            >
              {filteredItems.map(item => (
                <CheckList.Item key={item.value} value={item.value}>
                  {item.text}
                </CheckList.Item>
              ))}
            </CheckList>
          </div>

        </div>
      </Popup>
    </div>
  )
})

export default PopupCheckList;
