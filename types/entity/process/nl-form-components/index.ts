export enum NLFormHandler {
  formaccounts = 'formaccounts',
  formcascader = 'formcascader',
  formcheckbox = 'formcheckbox',
  formckeditor = 'formckeditor',
  formcollapse = 'formcollapse',
  formcube = 'formcube',
  formdate = 'formdate',
  formdivider = 'formdivider',
  formlabel = 'formlabel',
  formlink = 'formlink',
  formnumber = 'formnumber',
  formpassword = 'formpassword',
  formradio = 'formradio',
  formrate = 'formrate',
  formscript = 'formscript',
  formselect = 'formselect',
  formtab = 'formtab',
  formtableinputer = 'formtableinputer',
  formtableselector = 'formtableselector',
  formtext = 'formtext',
  formtextarea = 'formtextarea',
  formtime = 'formtime',
  formtreeselect = 'formtreeselect',
  formupload = 'formupload',
  formuserselect = 'formuserselect',
}

export enum NLFormLayoutHandler {
  formdivider = 'formdivider',
  formtab = 'formtab',
  formcollapse = 'formcollapse'
}
export interface NLFormComponent {
  handler: NLFormHandler | NLFormLayoutHandler;
  reaction: NLFormComponentReaction;
  override_config: NLFormComponentConfig;
  icon: string;
  hasValue: boolean;
  notUniqueKey?: boolean;
  label: string;
  type: string;
  category: string;
  config: NLFormComponentConfig;
  uuid: string;
  switchHandler?: string[];
  isDynamicValue?: boolean;
  key: string;
  excludedFromCondition?: boolean;
  isContainer?: boolean;
}

export interface NLFormComponentConfig {
  /**
   * 增加模糊的遮盖
   */
  isMask?: boolean;
  width: string;
  content?: string;
  isHide: boolean;
  isRequired?: boolean;
  /**
   * 仅展示值，看不见输入框
   */
  isReadOnly?: boolean;
  defaultValue?: NLFormComponentDefaultValue | string;
  description?: string;
  /**
   * 能看见，但是无法点击和使用
   */
  isDisabled?: boolean;
  hiddenFieldList?: any[];
  mapping?: NLFormComponentMapping;
  isMultiple?: boolean;
  matrixUuid?: string;
  tableKey?: string;
  isAddData?: boolean;
  sourceColumnList?: any[];
  dataList?: NLFormComponentMapping[];
  defaultValueType?: string;
  tagKey?: string;
  dataSource?: 'matrix' | 'static';
  dataSourceType?: string;
  levelType?: number;
  direction?: string;
  typeList?: NLFormComponentMapping[];
  optionList?: NLFormComponentMapping[];
  disableDefaultValue?: boolean;
  hideHeaderWhenDataEmpty?: boolean;
  pageSize?: number;
  matrixType?: string;
  dataConfig?: NLFormComponentDataConfig[];
  mode?: string;
  needPage?: boolean;
  styleType?: string;
  format?: string;
  placeholder?: string;
  validType?: Array<"workdate" | "custom">
  validValueList?: any[];
  text?: string;
  value?: string;
  target?: string;
  showText?: boolean;
  count?: number;
  allowHalf?: boolean;
  config?: {
    textName: string;
    valueName: string;
    url: string;
  };
  url?: string;
  isTemplate?: boolean;
  uploadType?: string;
  codeMode?: string;
  componentTopLeftTip?: string;
  groupList?: string[];
  dividerType?: string;
  dividerWidth?: number;
  isFontBold?: boolean;
  contentPosition?: string;
  fontColor?: string;
  dividerColor?: string;
  tabList?: NLFormComponentTabList[];
  isShowComponentNameInTab?: boolean;
  type?: string;
  isAccordion?: boolean;
  isSimple?: boolean;
  panelList?: NLFormComponentPaneList[];
  isAutoSelectdOnlyValue?: boolean;
  regex?: string;
  regexMessage?: string;
  maxLength?: number;
  minNumber?: number;
  maxNumber?: number;
  decimalNumber?: string;
}

export interface NLFormComponentPaneList {
  component: string[];
  text: string;
  value: string;
}

export interface NLFormComponentTabList {
  _visible: boolean;
  component: string[];
  text: string;
  value: string;
}

export interface NLFormComponentDataConfig {
  handler: string;
  matrixAttrUuid: string;
  isSearch: boolean;
  hasValue: boolean;
  label: string;
  isMobile: boolean;
  isSearchable: number;
  isPC: boolean;
  uuid: string;
  key: string;
  reaction?: NLFormComponentReaction;
  config?: {
    isRequired: boolean;
    isMask: boolean;
    isHide: boolean;
  };
}



export interface NLFormComponentReaction {
  hide: Record<string, any>;
  display: Record<string, any>;
  mask?:Record<string, any>;
  readonly?:Record<string, any>;
  setvalue?:Record<string, any>;
  disable?:Record<string, any>;
  emit?:Record<string, any>;
  required?:Record<string, any>;
  clearValue?:Record<string, any>;
  filter?:Record<string, any>;
  setValueOther?:Record<string, any>;
}



export interface FormCustomExtendConfig {
  extendConfigList: any[];
}


export interface HideComponentList {
  handler: string;
  reaction: NLFormComponentReaction;
  isHideComponent: boolean;
  icon: string;
  hasValue: boolean;
  label: string;
  type: string;
  category: string;
  isDynamicValue?: boolean;
  config: NLFormComponentConfig;
  uuid: string;
  key: string;
}

export interface NLFormComponentMapping {
  text: string;
  value: string;
  children?: NLFormComponentMapping[]
}

export interface NLFormComponentDefaultValue {
  _isHidden: boolean;
  _showtxtList: ShowtxtList[];
  text: string;
  value: string;
}

export interface ShowtxtList {
  Highlight: boolean;
  value: string;
}
